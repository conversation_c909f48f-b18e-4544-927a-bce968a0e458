/*
 * SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: Apache-2.0
 */

package com.mwam.kafkakewl.metrics.domain

fun KafkaConsumerGroupInfos.diff(newConsumerGroupInfos: KafkaConsumerGroupInfos): KafkaConsumerGroupInfoChanges {
    val addedOrUpdated = newConsumerGroupInfos.filter {
        val groupId = it.key
        val newConsumerGroupInfo = it.value
        val existingConsumerGroupInfo = this[groupId]
        existingConsumerGroupInfo == null || !existingConsumerGroupInfo.equalsWithoutTimestamp(newConsumerGroupInfo)
    }
    val removed = this.keys.subtract(newConsumerGroupInfos.keys)
    return KafkaConsumerGroupInfoChanges(addedOrUpdated, removed)
}

fun KafkaConsumerGroupInfos.applyChanges(changes: KafkaConsumerGroupInfoChanges): KafkaConsumerGroupInfos {
    return this.toMutableMap().apply {
        putAll(changes.addedOrUpdated)
        changes.removed.forEach { remove(it) }
    }.toMap()
}
