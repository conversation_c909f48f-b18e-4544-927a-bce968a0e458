/*
 * SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: Apache-2.0
 */

package com.mwam.kafkakewl.metrics.domain

import kotlinx.serialization.Serializable
import kotlinx.datetime.Instant
import org.apache.kafka.clients.admin.ConsumerGroupDescription
import org.apache.kafka.clients.consumer.OffsetAndMetadata
import org.apache.kafka.common.ConsumerGroupState
import org.apache.kafka.common.TopicPartition

@Serializable
data class KafkaConsumerGroupPartitionInfo(
    val topicPartition: KafkaTopicPartition,
    val committedOffset: Long,
    val metadata: String? = null,
    val lastCommitTimestamp: Instant
)

@Serializable
data class KafkaConsumerGroupInfo(
    val groupId: String,
    val state: String,
    val partitionInfos: List<KafkaConsumerGroupPartitionInfo>,
    val memberCount: Int,
    val coordinator: String? = null,
    val lastUpdateTimestamp: Instant
) {
    fun equalsWithoutTimestamp(other: KafkaConsumerGroupInfo): Boolean {
        return groupId == other.groupId &&
                state == other.state &&
                partitionInfos.map { it.copy(lastCommitTimestamp = Instant.DISTANT_PAST) } == 
                other.partitionInfos.map { it.copy(lastCommitTimestamp = Instant.DISTANT_PAST) } &&
                memberCount == other.memberCount &&
                coordinator == other.coordinator
    }
}

typealias KafkaConsumerGroupInfos = Map<String, KafkaConsumerGroupInfo>
val emptyKafkaConsumerGroupInfos: KafkaConsumerGroupInfos = emptyMap()

@Serializable
data class KafkaConsumerGroupInfoChanges(
    val addedOrUpdated: Map<String, KafkaConsumerGroupInfo>,
    val removed: Set<String>
) {
    val isEmpty
        get() = addedOrUpdated.isEmpty() && removed.isEmpty()

    override fun toString(): String = "added-or-updated: ${addedOrUpdated.size}, removed: ${removed.size}"
}

fun ConsumerGroupState.toSerializableString(): String = when (this) {
    ConsumerGroupState.UNKNOWN -> "UNKNOWN"
    ConsumerGroupState.PREPARING_REBALANCE -> "PREPARING_REBALANCE"
    ConsumerGroupState.COMPLETING_REBALANCE -> "COMPLETING_REBALANCE"
    ConsumerGroupState.STABLE -> "STABLE"
    ConsumerGroupState.DEAD -> "DEAD"
    ConsumerGroupState.EMPTY -> "EMPTY"
}

fun ConsumerGroupDescription.toKafkaConsumerGroupInfo(
    offsets: Map<TopicPartition, OffsetAndMetadata>,
    timestamp: Instant
): KafkaConsumerGroupInfo {
    val partitionInfos = offsets.map { (topicPartition, offsetAndMetadata) ->
        KafkaConsumerGroupPartitionInfo(
            topicPartition = KafkaTopicPartition(topicPartition),
            committedOffset = offsetAndMetadata.offset(),
            metadata = offsetAndMetadata.metadata(),
            lastCommitTimestamp = timestamp
        )
    }
    
    return KafkaConsumerGroupInfo(
        groupId = groupId(),
        state = state().toSerializableString(),
        partitionInfos = partitionInfos,
        memberCount = members().size,
        coordinator = coordinator()?.toString(),
        lastUpdateTimestamp = timestamp
    )
}
